<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide Stratégique OpenRouter - <PERSON>d<PERSON>les Gratuits</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0c0c1b; /* Un bleu nuit plus profond */
        }
        /* Style pour une scrollbar customisée */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1e1e3f; 
        }
        ::-webkit-scrollbar-thumb {
            background: #4a4a8a; 
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #6a6ad1; 
        }
        
        /* Classe pour le texte en dégradé Gemini */
        .gemini-gradient-text {
            background: linear-gradient(90deg, #4e8cff 0%, #a55eea 50%, #ff7eb9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .category-title {
            background: linear-gradient(90deg, rgba(78, 140, 255, 0.25) 0%, rgba(78, 140, 255, 0) 100%);
            border-left: 4px solid #4e8cff;
            padding: 1rem 1.5rem;
        }
        
        .card {
            background-color: rgba(25, 25, 50, 0.5);
            border: 1px solid #2a2a5a;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            border-color: #a55eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(165, 94, 234, 0.1);
        }
        
        .card-content {
            flex-grow: 1;
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="container mx-auto p-4 md:p-8">
        
        <!-- En-tête -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold mb-2 gemini-gradient-text">Guide Stratégique OpenRouter</h1>
            <h2 class="text-2xl md:text-3xl font-bold text-white mt-2">Partie 1 : Les Modèles Gratuits</h2>
            <p class="text-lg text-gray-400 mt-4">Le guide ultime pour choisir le bon modèle <strong class="text-white">gratuit</strong> pour la bonne tâche.</p>
        </header>

        <!-- Section des Modèles -->
        <section id="models-guide">
            <h2 class="text-3xl font-bold text-white mb-8 border-l-4 border-blue-500 pl-4">Analyse des Modèles Gratuits</h2>

            <!-- Catégorie S : Les Poids Lourds -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie S : Les Poids Lourds (State-of-the-Art)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Carte Modèle -->
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Meta: Llama 3.1 405B Instruct</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Raisonnement Complexe, Polyvalence</li>
                                <li><strong>Analyse Stratégique:</strong> Le summum des modèles open-source. Performance brute exceptionnelle. La gratuité est une opportunité unique.</li>
                                <li><strong>Usage Optimal:</strong> Analyse juridique/financière, agent IA complexe, contenu long de haute qualité.</li>
                                <li><strong>Pièges à Éviter:</strong> Tâches nécessitant une réponse instantanée (haute latence).</li>
                                <li><strong>Spécificités:</strong> Contexte de 65,536 tokens.</li>
                            </ul>
                        </div>
                    </div>
                    <!-- Carte Modèle -->
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">NVIDIA: Llama 3.1 Nemotron Ultra 253B v1</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Raisonnement Complexe, Codage</li>
                                <li><strong>Analyse Stratégique:</strong> Version NVIDIA d'un LLM massif. Performances de pointe, optimisations pour le code et la science.</li>
                                <li><strong>Usage Optimal:</strong> Recherche scientifique, résolution de problèmes de programmation complexes, simulation.</li>
                                <li><strong>Pièges à Éviter:</strong> Applications interactives à faible latence.</li>
                                <li><strong>Spécificités:</strong> Contexte de 131,072 tokens.</li>
                            </ul>
                        </div>
                    </div>
                    <!-- Carte Modèle -->
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Qwen: Qwen3 235B A22B</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Raisonnement Complexe, Multilingue</li>
                                <li><strong>Analyse Stratégique:</strong> Poids lourd d'Alibaba, réputé pour ses capacités multilingues. Concurrent direct de Meta et Google.</li>
                                <li><strong>Usage Optimal:</strong> Tâches de raisonnement de haut niveau pour un public international.</li>
                                <li><strong>Pièges à Éviter:</strong> Tâches simples et rapides.</li>
                                <li><strong>Spécificités:</strong> Contexte de 131,072 tokens.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie A : Haute Performance & Polyvalence -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie A : Haute Performance & Polyvalence</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Meta: Llama 3.3 70B Instruct</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Polyvalence, Chat de Haute Qualité</li>
                                <li><strong>Analyse Stratégique:</strong> Probablement le meilleur modèle "à tout faire". Puissant mais assez rapide. Un choix par défaut exceptionnel.</li>
                                <li><strong>Usage Optimal:</strong> Backend SaaS, chatbot avancé, analyse de données, génération de contenu.</li>
                                <li><strong>Pièges à Éviter:</strong> Surdimensionné pour des tâches simples (classification, routage).</li>
                                <li><strong>Spécificités:</strong> Contexte de 65,536 tokens.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Qwen: Qwen 2.5 72B Instruct</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Polyvalence, Multilingue</li>
                                <li><strong>Analyse Stratégique:</strong> Concurrent direct de Llama 70B. Solide si vous avez besoin de performances multilingues robustes.</li>
                                <li><strong>Usage Optimal:</strong> Similaire à Llama 70B, avec un accent sur les applications internationales.</li>
                                <li><strong>Pièges à Éviter:</strong> Tâches simples où la latence est critique.</li>
                                <li><strong>Spécificités:</strong> Contexte de 32,768 tokens.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Mistral: Mistral Nemo</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Polyvalence, Efficacité</li>
                                <li><strong>Analyse Stratégique:</strong> Conçu pour être très efficace. Performances de classe 70B avec une vitesse potentiellement meilleure.</li>
                                <li><strong>Usage Optimal:</strong> Applications d'entreprise, analyse de données, génération de code.</li>
                                <li><strong>Pièges à Éviter:</strong> Moins performant que les "Poids Lourds" sur le raisonnement extrême.</li>
                                <li><strong>Spécificités:</strong> Contexte de 131,072 tokens.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie B : Équilibrés & Rapides -->
            <div class="mb-12">
                 <h3 class="text-2xl font-bold mb-6 category-title">Catégorie B : Équilibrés & Rapides</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Mistral: Mistral Small 3.2 24B</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Chat Conversationnel, Économique/Rapide</li>
                                <li><strong>Analyse Stratégique:</strong> Le champion du rapport performance/vitesse. Assez rapide pour le temps réel, assez intelligent pour des tâches complexes.</li>
                                <li><strong>Usage Optimal:</strong> Chatbots, classification, résumé, backend IA à faible latence.</li>
                                <li><strong>Pièges à Éviter:</strong> Raisonnement profond en plusieurs étapes.</li>
                                <li><strong>Spécificités:</strong> Contexte jusqu'à 131,072 tokens.</li>
                            </ul>
                        </div>
                    </div>
                     <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Google: Gemma 2 9B & 3 12/27B</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Polyvalence, Équilibré</li>
                                <li><strong>Analyse Stratégique:</strong> Solides et fiables. Concurrents directs de Mistral Small. Bonnes capacités de raisonnement et de code pour leur taille.</li>
                                <li><strong>Usage Optimal:</strong> Chatbots généralistes, aide à la rédaction, analyse de données.</li>
                                <li><strong>Pièges à Éviter:</strong> Suivi d'instructions très complexes pour les plus petits.</li>
                                <li><strong>Spécificités:</strong> Contexte de 8k à 96k tokens.</li>
                            </ul>
                        </div>
                    </div>
                     <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Meta: Llama 3.2 11B Vision</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Vision (Multimodal)</li>
                                <li><strong>Analyse Stratégique:</strong> Modèle de vision très équilibré. Assez rapide pour des applications interactives et capable pour la plupart des tâches sur image.</li>
                                <li><strong>Usage Optimal:</strong> Description d'images, modération de contenu visuel, Q&R sur images.</li>
                                <li><strong>Pièges à Éviter:</strong> Analyse vidéo ou raisonnement visuel très complexe.</li>
                                <li><strong>Spécificités:</strong> Contexte de 131,072 tokens, accepte des images.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie C : Économiques & Ultra-Rapides -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie C : Économiques & Ultra-Rapides</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Mistral: Mistral 7B Instruct</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Ultra-Rapide, Efficace</li>
                                <li><strong>Analyse Stratégique:</strong> Le roi de la vitesse. Le meilleur choix pour les tâches quasi-instantanées. Étonnamment capable pour sa taille.</li>
                                <li><strong>Usage Optimal:</strong> Routage de requêtes, classification de sentiment, validation, chatbot de FAQ.</li>
                                <li><strong>Pièges à Éviter:</strong> Raisonnement, mathématiques, instructions complexes.</li>
                                <li><strong>Spécificités:</strong> Contexte de 32,768 tokens.</li>
                            </ul>
                        </div>
                    </div>
                     <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Meta: Llama 3.2 3B Instruct</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Ultra-Rapide</li>
                                <li><strong>Analyse Stratégique:</strong> Encore plus petit et plus rapide que Mistral 7B. À utiliser lorsque chaque milliseconde compte.</li>
                                <li><strong>Usage Optimal:</strong> Classification binaire, extraction de mots-clés.</li>
                                <li><strong>Pièges à Éviter:</strong> Tout ce qui n'est pas une tâche extrêmement simple.</li>
                                <li><strong>Spécificités:</strong> Contexte de 131,072 tokens (très grand pour sa taille).</li>
                            </ul>
                        </div>
                    </div>
                     <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Google/Qwen: 2B à 8B</h4>
                            <ul class="space-y-2 text-sm">
                                <li><strong>Catégorie:</strong> Ultra-Rapide</li>
                                <li><strong>Analyse Stratégique:</strong> Modèles d'entrée de gamme. Parfaits pour des tâches très simples ou pour des expérimentations.</li>
                                <li><strong>Usage Optimal:</strong> Formatage de texte, chatbots très basiques.</li>
                                <li><strong>Pièges à Éviter:</strong> Tâches factuelles ou de raisonnement.</li>
                                <li><strong>Spécificités:</strong> Contextes de 8k à 40k tokens.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Catégorie S+ : Les Spécialistes -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie S+ : Les Spécialistes</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Spécialistes du Code</h4>
                            <p class="text-sm text-gray-400 mb-3">Surpassent les généralistes pour la génération, l'explication et le débogage de code. À utiliser pour tout ce qui touche au développement.</p>
                            <ul class="text-xs list-disc list-inside text-purple-300 space-y-1">
                                <li><code>qwen/qwen-2.5-coder-32b-instruct</code></li>
                                <li><code>mistral/devstral-small</code></li>
                                <li><code>agentica/deepcoder-14b</code></li>
                                <li><code>qwen/qwen3-coder</code></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Spécialistes de la Vision</h4>
                            <p class="text-sm text-gray-400 mb-3">À la pointe de l'analyse d'images. Pour Q&R visuel complexe, analyse d'UI, description de scènes.</p>
                             <ul class="text-xs list-disc list-inside text-purple-300 space-y-1">
                                <li><code>qwen/qwen-2.5-vl-72b-instruct</code></li>
                                <li><code>qwen/qwen-2.5-vl-32b-instruct</code></li>
                                <li><code>moonshot-ai/kimi-vl-a3b</code></li>
                                <li><code>meta-llama/llama-3.2-11b-vision-instruct</code></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-blue-400 mb-3">Spécialistes Contexte Long</h4>
                           <p class="text-sm text-gray-400 mb-3">Leur super-pouvoir est leur immense fenêtre de contexte. Pour analyser des livres, des bases de code, de longues conversations.</p>
                            <ul class="text-xs list-disc list-inside text-purple-300 space-y-1">
                                <li><code>google/gemini-2.5-pro-experimental</code></li>
                                <li><code>google/gemini-2.0-flash-experimental</code></li>
                                <li><code>moonshot-ai/kimi-dev-72b</code></li>
                                <li><code>moonshot-ai/kimi-k2</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </section>

        <!-- Section des Combinaisons Stratégiques -->
        <section id="strategies">
            <h2 class="text-3xl font-bold text-white mb-8 mt-16 border-l-4 border-purple-500 pl-4">Combinaisons Stratégiques ("Pipelines d'IA")</h2>

            <div class="space-y-8">
                <!-- Stratégie 1 -->
                <div class="card rounded-lg p-6">
                    <h3 class="font-bold text-xl text-cyan-400 mb-4">Stratégie 1 : La "Triade du Développeur"</h3>
                    <p class="text-gray-300 mb-4">Pour passer de l'idée au code fonctionnel le plus rapidement possible.</p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">1. L'Architecte</p>
                            <p class="text-sm text-blue-300">Llama 3.3 70B</p>
                        </div>
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">2. Le Codeur</p>
                            <p class="text-sm text-blue-300">Qwen 2.5 Coder</p>
                        </div>
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">3. L'Assistant Agile</p>
                            <p class="text-sm text-blue-300">Mistral 7B</p>
                        </div>
                    </div>
                    <p class="mt-4 text-sm text-gray-400"><strong>Workflow:</strong> Utilisez Llama 70B pour la conception, Qwen Coder pour la génération de code, et Mistral 7B pour les micro-tâches (docstrings, tests unitaires).</p>
                </div>

                <!-- Stratégie 2 -->
                <div class="card rounded-lg p-6">
                    <h3 class="font-bold text-xl text-cyan-400 mb-4">Stratégie 2 : Le "Duo de Débogage"</h3>
                     <p class="text-gray-300 mb-4">Pour trouver et corriger des bugs complexes qui s'étendent sur plusieurs fichiers.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-center">
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">1. Le Premier Répondant</p>
                            <p class="text-sm text-blue-300">Qwen 2.5 Coder</p>
                        </div>
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">2. L'Enquêteur (Contexte Long)</p>
                            <p class="text-sm text-blue-300">Gemini 2.0 Flash</p>
                        </div>
                    </div>
                     <p class="mt-4 text-sm text-gray-400"><strong>Workflow:</strong> Utilisez Qwen Coder pour les erreurs simples. Si le bug persiste, donnez plusieurs fichiers à Gemini Flash pour une analyse de flux en profondeur.</p>
                </div>

                <!-- Stratégie 3 -->
                <div class="card rounded-lg p-6">
                    <h3 class="font-bold text-xl text-cyan-400 mb-4">Stratégie 3 : L' "Architecture du Routage Intelligent" (Production)</h3>
                     <p class="text-gray-300 mb-4">Pour une application IA rapide, intelligente et économique. La stratégie la plus importante pour un produit final.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">1. Le Portier (Routeur)</p>
                            <p class="text-sm text-blue-300">Mistral 7B</p>
                        </div>
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">2. Le Majordome</p>
                            <p class="text-sm text-blue-300">Mistral Small 24B</p>
                        </div>
                        <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">3. L'Expert</p>
                            <p class="text-sm text-blue-300">Llama 3.3 70B</p>
                        </div>
                         <div class="bg-gray-900/50 p-4 rounded-md">
                            <p class="font-semibold">4. Les Spécialistes</p>
                            <p class="text-sm text-blue-300">(Coder, Vision...)</p>
                        </div>
                    </div>
                     <p class="mt-4 text-sm text-gray-400"><strong>Workflow:</strong> Mistral 7B classifie la requête. Les requêtes simples vont à Mistral Small, les complexes à Llama 70B, et les spécifiques aux modèles spécialisés.</p>
                </div>
            </div>
        </section>

        <!-- Boutons de Navigation -->
        <div class="flex justify-center mt-8 space-x-4">
            <a href="https://flexodiv.netlify.app/" target="_blank" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Retour à l'accueil</a>
            <a href="https://openrouter.ai/models" target="_blank" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">Visiter OpenRouter</a>
        </div>
    </div>

</body>
</html>
