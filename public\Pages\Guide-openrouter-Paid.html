<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide Stratégique OpenRouter - Modèles Payants Abordables</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0c0c1b; /* Un bleu nuit plus profond */
        }
        /* Style pour une scrollbar customisée */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1e1e3f; 
        }
        ::-webkit-scrollbar-thumb {
            background: #4a4a8a; 
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #6a6ad1; 
        }
        
        /* Classe pour le texte en dégradé Gemini */
        .gemini-gradient-text {
            background: linear-gradient(90deg, #4e8cff 0%, #a55eea 50%, #ff7eb9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .category-title {
            background: linear-gradient(90deg, rgba(165, 94, 234, 0.25) 0%, rgba(165, 94, 234, 0) 100%);
            border-left: 4px solid #a55eea;
            padding: 1rem 1.5rem;
        }
        
        .card {
            background-color: rgba(25, 25, 50, 0.5);
            border: 1px solid #2a2a5a;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            border-color: #a55eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(165, 94, 234, 0.1);
        }
        
        .card-content {
            flex-grow: 1;
        }
        
        .price-tag {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 99px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
            margin-top: 8px;
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="container mx-auto p-4 md:p-8">
        
        <!-- En-tête -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold mb-2 gemini-gradient-text">Guide Stratégique OpenRouter</h1>
            <h2 class="text-2xl md:text-3xl font-bold text-white mt-2">Partie 2 : Les Modèles Payants Abordables</h2>
            <p class="text-lg text-gray-400 mt-4">Le guide pour les modèles <strong class="text-white">payants</strong> offrant le meilleur rapport performance/prix (de ~$0.07 à ~$1.25/M tokens).</p>
        </header>

        <!-- Section des Modèles -->
        <section id="models-guide">
            <h2 class="text-3xl font-bold text-white mb-8 border-l-4 border-purple-500 pl-4">Analyse des Modèles Payants Abordables</h2>

            <!-- Catégorie 1 : Les "Ultra-Économiques" -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie 1 : Les "Ultra-Économiques" (< $0.10/M tokens)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">HuggingFace / Nous: Zephyr & Hermes 7B</h4>
                            <p class="text-xs font-mono text-gray-400">huggingfaceh4/zephyr-7b-beta</p>
                            <p class="text-xs font-mono text-gray-400">nousresearch/hermes-2-pro-mistral-7b</p>
                            <span class="price-tag text-green-300">~ $0.07 / $0.07</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Routage d'Intention, Chat Créatif.</li>
                                <li><strong>Analyse:</strong> Le coût plancher absolu. Hermes est un fine-tune de Mistral 7B, bon en créativité. Zephyr est optimisé pour le suivi d'instructions.</li>
                                <li><strong>Usage Idéal:</strong> Routeur universel ("tâche simple ou complexe ?"), génération de variations marketing.</li>
                                <li><strong>Pièges:</strong> Raisonnement factuel, peuvent halluciner.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Mistral: Mistral 7B Instruct v0.3</h4>
                            <p class="text-xs font-mono text-gray-400">mistralai/mistral-7b-instruct-v0.3</p>
                            <span class="price-tag text-green-300">~ $0.07 / $0.07</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Utilitaire, Tâches Simples et Prévisibles.</li>
                                <li><strong>Analyse:</strong> Le standard de l'industrie pour les tâches simples. Moins "bavard" que Hermes, parfait pour une sortie structurée.</li>
                                <li><strong>Usage Idéal:</strong> Extraction d'entités (nom, email), classification de sentiment.</li>
                                <li><strong>Pièges:</strong> Demandes ouvertes ou nécessitant de la nuance.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Google: Gemma 7B Instruct</h4>
                            <p class="text-xs font-mono text-gray-400">google/gemma-7b-it</p>
                            <span class="price-tag text-green-300">~ $0.10 / $0.10</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Utilitaire, Bonne Base de Code.</li>
                                <li><strong>Analyse:</strong> Un choix "corporate" très sûr, soutenu par Google. Bonne réputation pour le code de base et une cohérence solide.</li>
                                <li><strong>Usage Idéal:</strong> Tâches utilitaires, complétion de code simple.</li>
                                <li><strong>Pièges:</strong> Raisonnement complexe.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie 2 : Les "Performants à Bas Coût" -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie 2 : Performants à Bas Coût (0.10$ - 0.30$/M)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Meta: Llama 3 8B Instruct</h4>
                            <p class="text-xs font-mono text-gray-400">meta-llama/llama-3-8b-instruct</p>
                            <span class="price-tag text-green-300">~ $0.13 / $0.13</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Meilleur "Petit" Modèle, Chat de Qualité, Code.</li>
                                <li><strong>Analyse:</strong> Une star. Bien plus performant que les 7B. Rapide, cohérent, et excellent en code pour sa taille.</li>
                                <li><strong>Usage Idéal:</strong> Chatbot de second niveau, assistant de codage pour fonctions simples.</li>
                                <li><strong>Pièges:</strong> Contexte de 8K, raisonnement multi-étapes profond.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Anthropic: Claude 3 Haiku</h4>
                            <p class="text-xs font-mono text-gray-400">anthropic/claude-3-haiku</p>
                            <span class="price-tag text-yellow-300">Input: $0.25 / Output: $1.25</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> ROI IMBATTABLE, Vision, Contexte Large (200K).</li>
                                <li><strong>Analyse:</strong> Change la donne. Vitesse fulgurante, vision, et immense contexte pour un coût d'input dérisoire. Attention au coût d'output 5x plus élevé.</li>
                                <li><strong>Usage Idéal:</strong> Analyse de documents (RAG), support client avec captures d'écran, cœur de 90% des apps IA.</li>
                                <li><strong>Pièges:</strong> Les réponses très longues peuvent devenir coûteuses.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie 3 : Le "Cœur du Réacteur" -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie 3 : Le "Cœur du Réacteur" (0.30$ - 0.60$/M)</h3>
                <div class="grid grid-cols-1">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Google: Gemini Pro</h4>
                            <p class="text-xs font-mono text-gray-400">google/gemini-pro</p>
                            <span class="price-tag text-yellow-300">~ $0.38 / $0.75</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Polyvalent Fiable, Intégration Écosystème Google.</li>
                                <li><strong>Analyse:</strong> Le modèle phare de Google. Très fiable, excellent support pour le "function calling". Un choix très sûr pour des applications pro.</li>
                                <li><strong>Usage Idéal:</strong> Agents autonomes appelant des API externes, backend pour apps d'entreprise.</li>
                                <li><strong>Pièges:</strong> Peut être moins "créatif" que les modèles Claude.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Catégorie 4 : Les "Spécialistes & Premium Abordables" -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold mb-6 category-title">Catégorie 4 : Premium Abordables (0.60$ - 1.00$/M)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Meta: Llama 3 70B Instruct</h4>
                            <p class="text-xs font-mono text-gray-400">meta-llama/llama-3-70b-instruct</p>
                            <span class="price-tag text-orange-300">~ $0.59 / $0.79</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> MOTEUR DE RAISONNEMENT PUR, Analyse Complexe, Code.</li>
                                <li><strong>Analyse:</strong> Le scalpel du chirurgien. Intelligence de niveau quasi-GPT-4 pour le raisonnement logique et le code à un prix incroyable.</li>
                                <li><strong>Usage Idéal:</strong> "Cerveau expert" dans un pipeline, analyse financière/scientifique, refactoring de code complexe.</li>
                                <li><strong>Pièges:</strong> Gaspillage de ressources pour des tâches simples. Pas de vision.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card rounded-lg p-6">
                        <div class="card-content">
                            <h4 class="font-bold text-lg text-cyan-300 mb-3">Mistral Large 2</h4>
                            <p class="text-xs font-mono text-gray-400">mistralai/mistral-large-2</p>
                            <span class="price-tag text-orange-300">~ $0.99 / $0.99</span>
                            <ul class="mt-4 space-y-2 text-sm">
                                <li><strong>Niche:</strong> Haute Performance, Multilingue (Europe).</li>
                                <li><strong>Analyse:</strong> Le modèle phare de Mistral, réputé pour ses excellentes performances en langues européennes (FR, DE, ES, IT).</li>
                                <li><strong>Usage Idéal:</strong> Chatbot ou agent pour un public européen, tâches de raisonnement complexes.</li>
                                <li><strong>Pièges:</strong> Coût à la limite supérieure de notre budget.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des Combinaisons Stratégiques -->
        <section id="strategies">
            <h2 class="text-3xl font-bold text-white mb-8 mt-16 border-l-4 border-pink-500 pl-4">Combinaisons Stratégiques "100% Productif"</h2>
            <div class="space-y-8">
                <div class="card rounded-lg p-6">
                    <h3 class="font-bold text-xl text-pink-400 mb-4">Synergie 1 : L'Usine à Code Intelligente et Économique</h3>
                    <p class="text-gray-300 mb-4">Utiliser le bon outil pour chaque étape du développement.</p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">1. L'Architecte</p><p class="text-sm text-cyan-300">meta-llama/llama-3-70b-instruct</p></div>
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">2. Le Codeur</p><p class="text-sm text-cyan-300">meta-llama/llama-3-8b-instruct</p></div>
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">3. Le Testeur/Doc</p><p class="text-sm text-cyan-300">anthropic/claude-3-haiku</p></div>
                    </div>
                    <p class="mt-4 text-sm text-gray-400"><strong>Workflow:</strong> Llama 70B pour la conception, Llama 8B pour la génération de masse, et Haiku pour lire le code final et générer tests et documentation.</p>
                </div>
                <div class="card rounded-lg p-6">
                    <h3 class="font-bold text-xl text-pink-400 mb-4">Synergie 2 : Le Support Client Automatisé à Plusieurs Niveaux</h3>
                    <p class="text-gray-300 mb-4">Résoudre 95% des tickets sans intervention humaine.</p>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">0. Le Portier</p><p class="text-sm text-cyan-300">mistralai/mistral-7b-instruct-v0.3</p></div>
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">1. L'Agent Efficace</p><p class="text-sm text-cyan-300">anthropic/claude-3-haiku</p></div>
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">2. L'Ingénieur</p><p class="text-sm text-cyan-300">meta-llama/llama-3-70b-instruct</p></div>
                        <div class="bg-gray-900/50 p-4 rounded-md"><p class="font-semibold">3. Le Communicant</p><p class="text-sm text-cyan-300">anthropic/claude-3-sonnet</p></div>
                    </div>
                    <p class="mt-4 text-sm text-gray-400"><strong>Workflow:</strong> Mistral 7B route la requête. Haiku (avec vision) résout 80% des cas. Llama 70B analyse les problèmes techniques complexes. Sonnet (hors budget mais bon à savoir) rédige l'email final empathique.</p>
                </div>
            </div>
        </section>

        <!-- Boutons de Navigation -->
        <div class="flex justify-center mt-8 space-x-4">
            <a href="https://flexodiv.netlify.app/" target="_blank" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Retour à l'accueil</a>
            <a href="https://openrouter.ai/models" target="_blank" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">Visiter OpenRouter</a>
        </div>
    </div>

</body>
</html>
